#!/usr/bin/env python3
# uart_test.py - 简单的UART通讯测试程序

from micu_uart_lib import SimpleUART, micu_printf
from maix import time, app

def main():
    print("UART通讯测试程序启动...")
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        uart.set_frame("$$", "##", True)
    else:
        print("串口初始化失败")
        return
    
    # 测试计数器
    test_count = 0
    
    print("开始发送测试数据...")
    print("按Ctrl+C退出程序")
    
    try:
        while not app.need_exit():
            # 发送不同类型的测试数据
            if test_count % 4 == 0:
                micu_printf("TEST")  # 简单测试
            elif test_count % 4 == 1:
                micu_printf("HELLO_STM32")  # 字符串测试
            elif test_count % 4 == 2:
                micu_printf(f"COUNT_{test_count}")  # 计数测试
            else:
                micu_printf("R,160,120")  # 模拟坐标数据
            
            test_count += 1
            print(f"已发送测试数据 #{test_count}")
            
            # 等待1秒
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
    finally:
        uart.close()
        print("串口已关闭，程序退出")

if __name__ == "__main__":
    main()
